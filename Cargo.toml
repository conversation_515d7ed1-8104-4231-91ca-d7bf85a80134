[package]
name = "candle-bert-time-series"
version = "0.1.0"
edition = "2021"

[dependencies]
# Core ML framework
candle-core = { version = "0.9.1", features = ["cuda"] }
candle-nn = { version = "0.9.1", features = ["cuda"] }
candle-transformers = { version = "0.9.1", features = ["cuda"] }

# High-frequency trading backtest framework
hftbacktest = "0.8.1"

# Numerical computing
ndarray = "0.16"

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Serialization
serde = { version = "1", features = ["derive"] }
serde_json = "1"

# Error handling
anyhow = "1"

# Data processing (for dataset creation and analysis)
polars = { version = "0.49.1", features = ["lazy", "csv", "temporal", "parquet"] }
chrono = "0.4"

# Utilities
rand = "0.8"
tracing = "0.1"